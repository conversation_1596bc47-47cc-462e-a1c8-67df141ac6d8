# 语音ROM Flash烧录问题修复指南

## 问题描述

从您的日志可以看出，ROM分区存在但魔数为 `0xFFFFFFFF`，这表明ROM文件没有正确烧录到Flash分区中。

## 解决方案

### 方法1：使用自动修复脚本（推荐）

```bash
# 运行修复脚本
python fix_voice_rom_flash.py
```

这个脚本会：
1. 检查esptool是否可用
2. 验证ROM文件状态
3. 自动查找ESP32端口
4. 上传ROM文件到Flash分区
5. 验证烧录结果

### 方法2：使用批处理脚本（Windows）

```cmd
# 双击运行
upload_voice_rom_manual.bat
```

按提示输入COM端口即可。

### 方法3：手动烧录

```bash
# 1. 确保ROM文件存在
ls -la src/voice_rom.bin

# 2. 手动烧录到分区
esptool.py --port COM3 --baud 921600 write_flash 0x5D0000 src/voice_rom.bin
```

**注意**：
- 将 `COM3` 替换为您的实际端口
- ROM分区地址是 `0x5D0000`（根据分区表配置）
- ROM文件大小不能超过320KB

### 方法4：使用PlatformIO自动烧录

我已经修改了 `platformio.ini`，现在上传脚本会在编译后自动运行：

```bash
# 编译并上传（包括ROM文件）
pio run -t upload

# 或者只上传ROM文件
pio run -t upload_voice_rom
```

## 验证修复结果

烧录完成后，重启ESP32，查看串口输出：

**修复前**（错误）：
```
[VoiceROM] Invalid magic number: 0xFFFFFFFF
[VoiceROM] Failed to load ROM from file system: -2
```

**修复后**（正常）：
```
[VoiceROM] ROM header validated successfully
[VoiceROM] Loaded X file entries
[VoiceROMTest] ✓ Found X/5 test files in ROM
```

## 常见问题

### Q1: esptool.py 不可用
```bash
pip install esptool
```

### Q2: 找不到ESP32端口
- Windows: 检查设备管理器中的COM端口
- Linux/Mac: 使用 `ls /dev/tty*` 查找端口

### Q3: ROM文件过大
检查 `src/voice_rom.bin` 文件大小，如果超过320KB需要：
1. 减少语音文件数量
2. 启用压缩（已默认启用）
3. 调整分区大小

### Q4: 权限问题（Linux/Mac）
```bash
sudo chmod 666 /dev/ttyUSB0  # 替换为实际端口
```

## 技术细节

- **ROM分区地址**: 0x5D0000
- **ROM分区大小**: 320KB (0x50000)
- **魔数**: 0x524F4D56 ("VROM")
- **分区类型**: ESP_PARTITION_TYPE_DATA, ESP_PARTITION_SUBTYPE_DATA_SPIFFS

## 下一步

修复完成后，您的语音系统将能够：
1. ✅ 从ROM分区快速加载预置语音文件
2. ✅ 在ROM文件不可用时自动回退到LittleFS
3. ✅ 提供更快的语音播放响应速度
4. ✅ 减少网络依赖

如果仍有问题，请检查：
1. 分区表配置是否正确
2. ROM文件是否有效
3. Flash分区是否损坏

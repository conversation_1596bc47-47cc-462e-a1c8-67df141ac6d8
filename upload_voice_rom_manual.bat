@echo off
echo 正在烧录语音ROM文件到ESP32分区...
echo.

REM 检查ROM文件是否存在
if not exist "src\voice_rom.bin" (
    echo 错误: src\voice_rom.bin 文件不存在
    echo 请先运行构建脚本生成ROM文件
    pause
    exit /b 1
)

REM 显示文件信息
for %%A in ("src\voice_rom.bin") do (
    echo ROM文件大小: %%~zA 字节
)

REM 检查文件大小（不能超过320KB = 327680字节）
for %%A in ("src\voice_rom.bin") do (
    if %%~zA GTR 327680 (
        echo 错误: ROM文件过大 (%%~zA > 327680)
        pause
        exit /b 1
    )
)

echo.
echo 请确保ESP32已连接并选择正确的COM端口
set /p COM_PORT=请输入COM端口 (例如: COM3): 

if "%COM_PORT%"=="" (
    echo 错误: 未指定COM端口
    pause
    exit /b 1
)

echo.
echo 正在烧录到 %COM_PORT%...
echo 命令: esptool.py --port %COM_PORT% --baud 921600 write_flash 0x5D0000 src\voice_rom.bin

esptool.py --port %COM_PORT% --baud 921600 write_flash 0x5D0000 src\voice_rom.bin

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 语音ROM烧录成功！
    echo.
    echo 现在可以重启ESP32，ROM系统应该能正常工作了。
) else (
    echo.
    echo ✗ 烧录失败！
    echo.
    echo 请检查:
    echo 1. ESP32是否正确连接
    echo 2. COM端口是否正确
    echo 3. 是否安装了esptool: pip install esptool
)

echo.
pause

#ifndef __VOICE_HYBRID_SYSTEM_H__
#define __VOICE_HYBRID_SYSTEM_H__

#include <Arduino.h>
#include <esp_err.h>
#include <esp_partition.h>
#include <FS.h>
#include <LittleFS.h>

#ifdef __cplusplus
extern "C"
{
#endif

// 版本和魔数定义
#define VOICE_ROM_MAGIC 0x564F4943 // "VOIC"
#define VOICE_ROM_VERSION_MAJOR 1
#define VOICE_ROM_VERSION_MINOR 0

// 压缩类型定义
#define VOICE_COMPRESSION_NONE 0
#define VOICE_COMPRESSION_ADPCM 1
#define VOICE_COMPRESSION_ULAW 2
#define VOICE_COMPRESSION_OPUS 3

// 默认配置
#define DEFAULT_VOICE_COMPRESSION VOICE_COMPRESSION_ADPCM
#define VOICE_ROM_PARTITION_NAME "voice_rom"
#define VOICE_CACHE_SIZE_KB 64
#define MAX_VOICE_FILES 32
#define MAX_FILENAME_LEN 32
#define MAX_FILEPATH_LEN 64

    // 错误代码定义
    typedef enum
    {
        VOICE_ERR_OK = 0,
        VOICE_ERR_INVALID_PARAM = -1,
        VOICE_ERR_FILE_NOT_FOUND = -2,
        VOICE_ERR_DECOMPRESSION_FAILED = -3,
        VOICE_ERR_CHECKSUM_MISMATCH = -4,
        VOICE_ERR_INSUFFICIENT_MEMORY = -5,
        VOICE_ERR_NETWORK_TIMEOUT = -6,
        VOICE_ERR_STORAGE_FULL = -7,
        VOICE_ERR_PARTITION_NOT_FOUND = -8,
        VOICE_ERR_INVALID_HEADER = -9,
        VOICE_ERR_INIT_FAILED = -10
    } voice_error_t;

    // 语音源类型
    typedef enum
    {
        VOICE_SOURCE_RAM_CACHE,    // RAM缓存中的文件
        VOICE_SOURCE_LITTLEFS,     // LittleFS中的动态文件
        VOICE_SOURCE_FLASH_ROM,    // Flash ROM中的预置文件
        VOICE_SOURCE_DEFAULT_TONE, // 默认提示音
        VOICE_SOURCE_NONE          // 无可用源
    } voice_source_type_t;

    // 版本比较结果
    typedef enum
    {
        VERSION_SAME = 0,
        VERSION_ROM_NEWER = 1,
        VERSION_CLOUD_NEWER = 2,
        VERSION_INVALID = -1
    } voice_version_compare_result_t;

    // 优先级策略
    typedef enum
    {
        VOICE_PRIORITY_SPEED_FIRST,      // 速度优先：RAM > LittleFS > ROM
        VOICE_PRIORITY_QUALITY_FIRST,    // 质量优先：LittleFS > ROM > RAM
        VOICE_PRIORITY_RELIABILITY_FIRST // 可靠性优先：ROM > LittleFS > RAM
    } voice_priority_policy_t;

    // ROM文件头结构
    typedef struct
    {
        uint32_t magic;         // 魔数标识
        uint16_t major_version; // 主版本号
        uint16_t minor_version; // 次版本号
        uint32_t build_number;  // 构建号
        uint32_t timestamp;     // 构建时间戳
        uint32_t file_count;    // 文件数量
        uint32_t total_size;    // 总大小
        uint32_t checksum;      // 校验和
        uint8_t reserved[8];    // 保留字段
    } __attribute__((packed)) voice_rom_header_t;

    // ROM文件条目结构
    typedef struct
    {
        char filename[MAX_FILENAME_LEN]; // 文件名
        uint32_t offset;                 // 在ROM中的偏移
        uint32_t compressed_size;        // 压缩后大小
        uint32_t original_size;          // 原始大小
        uint32_t checksum;               // 文件校验和
        uint16_t version;                // 文件版本
        uint8_t compression_type;        // 压缩类型
        uint8_t reserved;                // 保留字段
    } __attribute__((packed)) voice_file_entry_t;

    // 语音文件信息
    typedef struct
    {
        voice_source_type_t source;       // 文件源类型
        char file_path[MAX_FILEPATH_LEN]; // 文件路径
        uint32_t file_size;               // 文件大小
        uint16_t version;                 // 文件版本
        bool is_compressed;               // 是否压缩
        uint32_t checksum;                // 校验和
    } voice_file_info_t;

    // 存储状态信息
    typedef struct
    {
        uint32_t rom_total_size;      // ROM总大小
        uint32_t rom_used_size;       // ROM已用大小
        uint32_t littlefs_total_size; // LittleFS总大小
        uint32_t littlefs_used_size;  // LittleFS已用大小
        uint32_t cache_total_size;    // 缓存总大小
        uint32_t cache_used_size;     // 缓存已用大小
        uint16_t rom_file_count;      // ROM文件数量
        uint16_t littlefs_file_count; // LittleFS文件数量
    } voice_storage_info_t;

    // 缓存条目结构
    typedef struct voice_cache_entry
    {
        char filename[MAX_FILENAME_LEN];
        uint8_t *data;
        uint32_t size;
        uint32_t last_access_time;
        struct voice_cache_entry *next;
    } voice_cache_entry_t;

    // 系统配置结构
    typedef struct
    {
        voice_priority_policy_t priority_policy;
        uint32_t cache_size_kb;
        bool compression_enabled;
        bool auto_update_enabled;
        uint32_t update_check_interval;
    } voice_system_config_t;

    // 全局变量声明
    extern voice_system_config_t system_config;
    extern bool system_initialized;

    // 核心API函数声明

    /**
     * @brief 初始化语音混合系统
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_hybrid_init(void);

    /**
     * @brief 反初始化语音混合系统
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_hybrid_deinit(void);

    /**
     * @brief 播放语音文件
     * @param voice_name 语音文件名
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_play(const char *voice_name);

    /**
     * @brief 智能播放语音文件（带自动回退）
     * @param voice_name 语音文件名
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_play_smart(const char *voice_name);

    /**
     * @brief 检查文件版本
     * @param voice_name 语音文件名
     * @return voice_version_compare_result_t 版本比较结果
     */
    voice_version_compare_result_t voice_check_version(const char *voice_name);

    /**
     * @brief 从云端更新语音文件
     * @param voice_name 语音文件名
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_update_from_cloud(const char *voice_name);

    /**
     * @brief 批量更新所有语音文件
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_update_all_from_cloud(void);

    /**
     * @brief 获取存储状态信息
     * @param info 存储状态信息结构指针
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_get_storage_info(voice_storage_info_t *info);

    /**
     * @brief 获取语音文件信息
     * @param voice_name 语音文件名
     * @param info 文件信息结构指针
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_get_file_info(const char *voice_name, voice_file_info_t *info);

    /**
     * @brief 设置优先级策略
     * @param policy 优先级策略
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_set_priority_policy(voice_priority_policy_t policy);

    /**
     * @brief 设置缓存大小
     * @param size_kb 缓存大小(KB)
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_set_cache_size(uint32_t size_kb);

    /**
     * @brief 启用/禁用压缩
     * @param enabled 是否启用压缩
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_set_compression_enabled(bool enabled);

    /**
     * @brief 清理缓存
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_clear_cache(void);

    /**
     * @brief 预加载语音文件到缓存
     * @param voice_name 语音文件名
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_preload(const char *voice_name);

    // ROM文件系统相关函数

    /**
     * @brief 初始化ROM文件系统
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_rom_init(void);

    /**
     * @brief 从ROM读取文件
     * @param filename 文件名
     * @param buffer 输出缓冲区
     * @param buffer_size 缓冲区大小
     * @param actual_size 实际读取大小
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_rom_read_file(const char *filename, uint8_t *buffer,
                                      uint32_t buffer_size, uint32_t *actual_size);

    /**
     * @brief 检查ROM中是否存在文件
     * @param filename 文件名
     * @return bool 是否存在
     */
    bool voice_rom_file_exists(const char *filename);

    /**
     * @brief 获取ROM文件信息
     * @param filename 文件名
     * @param entry 文件条目信息
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_rom_get_file_entry(const char *filename, voice_file_entry_t *entry);

    /**
     * @brief 获取ROM系统信息
     * @param total_size ROM总大小
     * @param used_size ROM已用大小
     * @param file_count ROM文件数量
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_rom_get_info(uint32_t *total_size, uint32_t *used_size, uint16_t *file_count);

    /**
     * @brief 反初始化ROM系统
     */
    void voice_rom_deinit(void);

    /**
     * @brief 检查ROM系统是否已初始化
     * @return bool 是否已初始化
     */
    bool voice_rom_is_initialized(void);

    // 压缩相关函数

    /**
     * @brief 解压缩数据
     * @param compressed_data 压缩数据
     * @param compressed_size 压缩数据大小
     * @param output_buffer 输出缓冲区
     * @param output_size 输出缓冲区大小
     * @param compression_type 压缩类型
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_decompress(const uint8_t *compressed_data, uint32_t compressed_size,
                                   uint8_t *output_buffer, uint32_t output_size,
                                   uint8_t compression_type);

    /**
     * @brief 压缩数据
     * @param input_data 输入数据
     * @param input_size 输入数据大小
     * @param output_buffer 输出缓冲区
     * @param output_size 输出缓冲区大小
     * @param compression_type 压缩类型
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_compress(const uint8_t *input_data, uint32_t input_size,
                                 uint8_t *output_buffer, uint32_t *output_size,
                                 uint8_t compression_type);

    // 存储管理相关函数

    /**
     * @brief 监控存储空间状态
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_monitor_storage(void);

    /**
     * @brief 清理存储空间
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_cleanup_storage(void);

    /**
     * @brief 获取存储统计信息
     * @param info 存储状态信息结构指针
     * @return voice_error_t 错误代码
     */
    voice_error_t voice_get_storage_stats(voice_storage_info_t *info);

    // 错误处理和日志相关函数

    /**
     * @brief 记录语音系统错误
     * @param error 错误代码
     * @param context 错误上下文
     * @param details 错误详情
     */
    void voice_log_error(voice_error_t error, const char *context, const char *details);

    /**
     * @brief 记录语音系统信息
     * @param context 信息上下文
     * @param message 信息内容
     */
    void voice_log_info(const char *context, const char *message);

    /**
     * @brief 记录语音系统警告
     * @param context 警告上下文
     * @param message 警告内容
     */
    void voice_log_warning(const char *context, const char *message);

    /**
     * @brief 配置日志系统
     * @param log_to_file 是否记录到文件
     * @param log_to_serial 是否输出到串口
     * @param max_file_size 最大文件大小
     * @param level 日志级别
     */
    void voice_configure_logging(bool log_to_file, bool log_to_serial,
                                 uint32_t max_file_size, esp_log_level_t level);

    /**
     * @brief 重置错误统计
     */
    void voice_reset_error_stats(void);

    /**
     * @brief 导出错误日志
     * @return String 日志内容
     */
    String voice_export_error_log(void);

    /**
     * @brief 清理旧日志文件
     */
    void voice_cleanup_logs(void);

    // 测试和调试函数

    /**
     * @brief 运行语音ROM系统测试
     */
    void run_voice_rom_tests(void);

    /**
     * @brief 运行语音ROM系统完整测试（包含状态检查）
     */
    void run_voice_rom_complete_tests(void);

    /**
     * @brief 语音系统状态报告（在main.cpp中实现）
     */
    void voice_system_status_report(void);

#ifdef __cplusplus
}
#endif

#endif // __VOICE_HYBRID_SYSTEM_H__

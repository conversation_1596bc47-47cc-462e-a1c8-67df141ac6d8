#!/usr/bin/env python3
"""
语音ROM Flash烧录修复脚本
解决ROM文件没有正确烧录到Flash分区的问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_esptool():
    """检查esptool是否可用"""
    try:
        result = subprocess.run(["esptool.py", "--help"], 
                              capture_output=True, text=True, check=True)
        print("✓ esptool.py 可用")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ esptool.py 不可用")
        print("请安装: pip install esptool")
        return False

def find_esp32_port():
    """自动查找ESP32端口"""
    try:
        result = subprocess.run(["pio", "device", "list"], 
                              capture_output=True, text=True, check=True)
        lines = result.stdout.split('\n')
        ports = []
        
        for line in lines:
            if 'USB' in line and ('COM' in line or '/dev/tty' in line):
                # Windows COM端口
                if 'COM' in line:
                    parts = line.split()
                    for part in parts:
                        if part.startswith('COM'):
                            ports.append(part)
                # Linux/Mac端口
                elif '/dev/tty' in line:
                    parts = line.split()
                    for part in parts:
                        if part.startswith('/dev/tty'):
                            ports.append(part)
        
        return ports
    except:
        return []

def check_rom_files():
    """检查ROM文件状态"""
    print("\n=== 检查ROM文件状态 ===")
    
    files_to_check = [
        "src/voice_rom.bin",
        "data/voice_rom.bin"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {file_path}: {size} 字节")
            
            # 检查文件是否为空或无效
            if size == 0:
                print(f"  ⚠ 警告: {file_path} 文件为空")
            elif size > 327680:  # 320KB
                print(f"  ⚠ 警告: {file_path} 文件过大 ({size} > 327680)")
        else:
            print(f"✗ {file_path}: 不存在")
    
    return os.path.exists("src/voice_rom.bin")

def build_voice_rom():
    """构建语音ROM文件"""
    print("\n=== 构建语音ROM文件 ===")
    
    try:
        # 运行构建脚本
        result = subprocess.run([
            "python", "src/scripts/build_voice_rom.py", 
            "--project-dir", "."
        ], check=True, capture_output=True, text=True)
        
        print("✓ ROM文件构建成功")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print("✗ ROM文件构建失败")
        print(f"错误: {e.stderr}")
        return False

def upload_rom_to_flash(port):
    """上传ROM文件到Flash分区"""
    print(f"\n=== 上传ROM到Flash分区 (端口: {port}) ===")
    
    rom_file = "src/voice_rom.bin"
    if not os.path.exists(rom_file):
        print(f"✗ ROM文件不存在: {rom_file}")
        return False
    
    # 构建esptool命令
    cmd = [
        "esptool.py",
        "--port", port,
        "--baud", "921600",
        "write_flash",
        "0x5D0000",  # voice_rom分区起始地址
        rom_file
    ]
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ ROM文件上传成功!")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print("✗ ROM文件上传失败")
        print(f"错误: {e.stderr}")
        return False

def verify_flash_partition(port):
    """验证Flash分区内容"""
    print(f"\n=== 验证Flash分区内容 (端口: {port}) ===")
    
    try:
        # 读取分区前16字节（包含魔数）
        cmd = [
            "esptool.py",
            "--port", port,
            "--baud", "921600",
            "read_flash",
            "0x5D0000",  # voice_rom分区起始地址
            "16",        # 读取16字节
            "temp_header.bin"
        ]
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        # 检查魔数
        if os.path.exists("temp_header.bin"):
            with open("temp_header.bin", "rb") as f:
                header = f.read(4)
                if len(header) >= 4:
                    magic = int.from_bytes(header, byteorder='little')
                    if magic == 0x524F4D56:  # "VROM"
                        print("✓ Flash分区魔数正确")
                        os.remove("temp_header.bin")
                        return True
                    else:
                        print(f"✗ Flash分区魔数错误: 0x{magic:08X} (期望: 0x524F4D56)")
                else:
                    print("✗ 无法读取魔数")
            
            os.remove("temp_header.bin")
        
        return False
        
    except subprocess.CalledProcessError as e:
        print("✗ 验证失败")
        print(f"错误: {e.stderr}")
        return False

def main():
    print("=== 语音ROM Flash烧录修复工具 ===")
    print("此工具将帮助您解决ROM文件没有正确烧录到Flash的问题\n")
    
    # 1. 检查工具
    if not check_esptool():
        return 1
    
    # 2. 检查ROM文件
    if not check_rom_files():
        print("\n需要先构建ROM文件...")
        if not build_voice_rom():
            return 1
    
    # 3. 查找端口
    ports = find_esp32_port()
    if not ports:
        port = input("\n未找到ESP32端口，请手动输入 (例如: COM3): ").strip()
        if not port:
            print("错误: 未指定端口")
            return 1
    else:
        if len(ports) == 1:
            port = ports[0]
            print(f"\n找到ESP32端口: {port}")
        else:
            print(f"\n找到多个端口: {ports}")
            port = input("请选择端口: ").strip()
            if port not in ports:
                print("错误: 无效端口")
                return 1
    
    # 4. 上传ROM文件
    if not upload_rom_to_flash(port):
        return 1
    
    # 5. 验证上传结果
    print("\n等待3秒后验证...")
    time.sleep(3)
    
    if verify_flash_partition(port):
        print("\n🎉 ROM文件烧录成功！")
        print("现在可以重启ESP32，语音ROM系统应该能正常工作了。")
        return 0
    else:
        print("\n⚠ 验证失败，可能需要重试")
        return 1

if __name__ == "__main__":
    sys.exit(main())

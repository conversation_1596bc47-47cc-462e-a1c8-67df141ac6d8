/**
 * @file test_voice_rom_fix.cpp
 * @brief 测试语音ROM系统修复效果
 * 
 * 这个文件包含了测试代码，用于验证语音ROM系统的修复效果
 */

#include <Arduino.h>
#include "voice_hybrid_system.h"

static const char* TAG = "VoiceROMTest";

// 外部声明
extern bool system_initialized;

/**
 * @brief 测试ROM系统状态
 */
void test_rom_system_status() {
    Serial.println("\n=== 测试ROM系统状态 ===");
    
    // 检查ROM系统是否初始化
    bool rom_initialized = voice_rom_is_initialized();
    Serial.printf("ROM系统初始化状态: %s\n", rom_initialized ? "✅ 已初始化" : "❌ 未初始化");
    
    if (rom_initialized) {
        // 获取ROM系统信息
        uint32_t total_size, used_size;
        uint16_t file_count;
        
        voice_error_t err = voice_rom_get_info(&total_size, &used_size, &file_count);
        if (err == VOICE_ERR_OK) {
            Serial.printf("ROM信息:\n");
            Serial.printf("  总大小: %d 字节\n", total_size);
            Serial.printf("  已用大小: %d 字节\n", used_size);
            Serial.printf("  文件数量: %d\n", file_count);
        } else {
            Serial.printf("❌ 获取ROM信息失败，错误代码: %d\n", err);
        }
        
        // 测试ROM文件存在性
        const char* test_files[] = {
            "select_user",
            "network_success",
            "open_app_to_config"
        };
        
        Serial.println("ROM文件存在性检查:");
        for (int i = 0; i < 3; i++) {
            bool exists = voice_rom_file_exists(test_files[i]);
            Serial.printf("  %s: %s\n", test_files[i], exists ? "✅ 存在" : "❌ 不存在");
        }
    }
    
    Serial.println("=== ROM系统状态测试完成 ===\n");
}

/**
 * @brief 测试LittleFS文件系统
 */
void test_littlefs_files() {
    Serial.println("\n=== 测试LittleFS文件系统 ===");
    
    // 检查LittleFS是否挂载
    if (!LittleFS.begin()) {
        Serial.println("❌ LittleFS未挂载");
        return;
    }
    
    Serial.println("✅ LittleFS已挂载");
    
    // 列出根目录文件
    Serial.println("根目录文件列表:");
    File root = LittleFS.open("/");
    if (root && root.isDirectory()) {
        File file = root.openNextFile();
        while (file) {
            Serial.printf("  %s (%d 字节)\n", file.name(), file.size());
            file = root.openNextFile();
        }
    } else {
        Serial.println("❌ 无法打开根目录");
    }
    
    // 检查音频目录
    extern String audio_path;
    String audio_dir = "/" + audio_path;
    Serial.printf("音频目录 (%s) 文件列表:\n", audio_dir.c_str());
    
    File audioDir = LittleFS.open(audio_dir);
    if (audioDir && audioDir.isDirectory()) {
        File file = audioDir.openNextFile();
        while (file) {
            if (String(file.name()).endsWith(".wav")) {
                Serial.printf("  ✅ %s (%d 字节)\n", file.name(), file.size());
            }
            file = audioDir.openNextFile();
        }
    } else {
        Serial.printf("❌ 无法打开音频目录: %s\n", audio_dir.c_str());
    }
    
    // 检查ROM文件
    const char* rom_paths[] = {
        "/voice_rom.bin",
        "voice_rom.bin",
        "/data/voice_rom.bin",
        "data/voice_rom.bin"
    };
    
    Serial.println("ROM文件查找:");
    bool found_rom = false;
    for (int i = 0; i < 4; i++) {
        if (LittleFS.exists(rom_paths[i])) {
            File romFile = LittleFS.open(rom_paths[i], "r");
            if (romFile) {
                Serial.printf("  ✅ 找到ROM文件: %s (%d 字节)\n", rom_paths[i], romFile.size());
                romFile.close();
                found_rom = true;
            }
        }
    }
    
    if (!found_rom) {
        Serial.println("  ❌ 未找到ROM文件");
    }
    
    Serial.println("=== LittleFS测试完成 ===\n");
}

/**
 * @brief 测试语音源选择
 */
void test_voice_source_selection() {
    Serial.println("\n=== 测试语音源选择 ===");
    
    if (!system_initialized) {
        Serial.println("❌ 语音混合系统未初始化");
        return;
    }
    
    const char* test_voice = "select_user";
    
    // 获取语音文件信息
    voice_file_info_t file_info;
    voice_error_t err = voice_get_file_info(test_voice, &file_info);
    
    if (err == VOICE_ERR_OK) {
        Serial.printf("✅ 语音文件信息获取成功: %s\n", test_voice);
        Serial.printf("  源类型: %d\n", file_info.source);
        Serial.printf("  文件路径: %s\n", file_info.file_path);
        Serial.printf("  文件大小: %d 字节\n", file_info.file_size);
        Serial.printf("  版本: %d\n", file_info.version);
        Serial.printf("  是否压缩: %s\n", file_info.is_compressed ? "是" : "否");
        
        // 解释源类型
        const char* source_names[] = {
            "未知", "RAM缓存", "LittleFS", "Flash ROM", "默认提示音"
        };
        if (file_info.source >= 0 && file_info.source <= 4) {
            Serial.printf("  源类型说明: %s\n", source_names[file_info.source]);
        }
    } else {
        Serial.printf("❌ 语音文件信息获取失败: %s，错误代码: %d\n", test_voice, err);
    }
    
    Serial.println("=== 语音源选择测试完成 ===\n");
}

/**
 * @brief 测试语音播放功能
 */
void test_voice_playback() {
    Serial.println("\n=== 测试语音播放功能 ===");
    
    if (!system_initialized) {
        Serial.println("❌ 语音混合系统未初始化");
        return;
    }
    
    const char* test_voice = "select_user";
    
    // 测试智能播放
    Serial.printf("测试智能播放: %s\n", test_voice);
    voice_error_t err = voice_play_smart(test_voice);
    
    if (err == VOICE_ERR_OK) {
        Serial.printf("✅ 智能播放启动成功: %s\n", test_voice);
    } else {
        Serial.printf("❌ 智能播放失败: %s，错误代码: %d\n", test_voice, err);
        
        // 解释错误代码
        switch (err) {
            case VOICE_ERR_INVALID_PARAM:
                Serial.println("  错误原因: 参数无效");
                break;
            case VOICE_ERR_FILE_NOT_FOUND:
                Serial.println("  错误原因: 文件未找到");
                break;
            case VOICE_ERR_INIT_FAILED:
                Serial.println("  错误原因: 初始化失败");
                break;
            default:
                Serial.println("  错误原因: 未知错误");
                break;
        }
    }
    
    Serial.println("=== 语音播放测试完成 ===\n");
}

/**
 * @brief 运行所有ROM修复测试
 */
void run_all_rom_fix_tests() {
    Serial.println("\n🔧 开始语音ROM系统修复测试...\n");
    
    // 等待系统稳定
    delay(2000);
    
    // 运行各项测试
    test_rom_system_status();
    delay(1000);
    
    test_littlefs_files();
    delay(1000);
    
    test_voice_source_selection();
    delay(1000);
    
    test_voice_playback();
    delay(1000);
    
    Serial.println("🎉 所有ROM修复测试完成！");
}

/**
 * @brief 打印系统综合状态
 */
void print_voice_system_comprehensive_status() {
    Serial.println("\n=== 语音系统综合状态 ===");
    
    // 基本状态
    Serial.printf("语音混合系统: %s\n", system_initialized ? "✅ 已初始化" : "❌ 未初始化");
    Serial.printf("ROM系统: %s\n", voice_rom_is_initialized() ? "✅ 已初始化" : "❌ 未初始化");
    
    extern uint8_t audio_enable_flag;
    Serial.printf("音频启用: %s\n", audio_enable_flag ? "✅ 启用" : "❌ 禁用");
    
    extern String audio_path;
    Serial.printf("音频路径: %s\n", audio_path.c_str());
    
    // 存储信息
    if (system_initialized) {
        voice_storage_info_t storage_info;
        if (voice_get_storage_info(&storage_info) == VOICE_ERR_OK) {
            Serial.println("存储信息:");
            Serial.printf("  ROM: %d/%d 字节 (%d 文件)\n", 
                         storage_info.rom_used_size, 
                         storage_info.rom_total_size,
                         storage_info.rom_file_count);
            Serial.printf("  LittleFS: %d/%d 字节 (%d 文件)\n", 
                         storage_info.littlefs_used_size, 
                         storage_info.littlefs_total_size,
                         storage_info.littlefs_file_count);
            Serial.printf("  缓存: %d/%d 字节\n", 
                         storage_info.cache_used_size, 
                         storage_info.cache_total_size);
        }
    }
    
    Serial.println("=== 综合状态信息结束 ===\n");
}

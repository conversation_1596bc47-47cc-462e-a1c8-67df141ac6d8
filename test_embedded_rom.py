#!/usr/bin/env python3
"""
测试嵌入式ROM文件的脚本
验证ROM文件是否正确嵌入到固件中
"""

import os
import struct
import subprocess
import sys

def check_rom_file():
    """检查ROM文件是否存在且有效"""
    rom_file = "src/voice_rom.bin"
    
    if not os.path.exists(rom_file):
        print(f"❌ ROM文件不存在: {rom_file}")
        return False
    
    file_size = os.path.getsize(rom_file)
    print(f"✅ ROM文件存在: {rom_file} ({file_size} 字节)")
    
    # 检查魔数
    try:
        with open(rom_file, 'rb') as f:
            magic_bytes = f.read(4)
            if len(magic_bytes) == 4:
                magic = struct.unpack('<I', magic_bytes)[0]
                expected_magic = 0x524F4D56  # "VROM"
                if magic == expected_magic:
                    print(f"✅ ROM文件魔数正确: 0x{magic:08X}")
                    return True
                else:
                    print(f"❌ ROM文件魔数错误: 0x{magic:08X} (期望: 0x{expected_magic:08X})")
            else:
                print("❌ 无法读取ROM文件魔数")
    except Exception as e:
        print(f"❌ 读取ROM文件失败: {e}")
    
    return False

def check_platformio_config():
    """检查PlatformIO配置"""
    config_file = "platformio.ini"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("src/voice_rom.bin", "ROM文件嵌入配置"),
        ("pre:src/scripts/build_voice_rom.py", "ROM构建脚本"),
        ("VOICE_HYBRID_SYSTEM_ENABLED=1", "语音混合系统启用"),
    ]
    
    all_ok = True
    for check, description in checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - 缺失")
            all_ok = False
    
    return all_ok

def build_and_check():
    """构建项目并检查嵌入"""
    print("\n=== 构建项目 ===")
    
    try:
        # 清理构建
        print("清理构建...")
        result = subprocess.run(["pio", "run", "-t", "clean"], 
                              capture_output=True, text=True, check=True)
        
        # 构建项目
        print("构建项目...")
        result = subprocess.run(["pio", "run"], 
                              capture_output=True, text=True, check=True)
        
        print("✅ 构建成功")
        
        # 检查构建输出中是否包含ROM文件
        if "voice_rom.bin" in result.stdout:
            print("✅ 构建输出中包含ROM文件处理")
        else:
            print("⚠️ 构建输出中未明确提及ROM文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_binary_symbols():
    """检查二进制文件中的符号"""
    print("\n=== 检查二进制符号 ===")
    
    # 查找固件文件
    firmware_paths = [
        ".pio/build/esp32-s3-devkitc-1-N16R8/firmware.elf",
        ".pio/build/esp32-s3-devkitc-1-N16R8/firmware.bin"
    ]
    
    firmware_file = None
    for path in firmware_paths:
        if os.path.exists(path):
            firmware_file = path
            break
    
    if not firmware_file:
        print("❌ 找不到固件文件")
        return False
    
    print(f"✅ 找到固件文件: {firmware_file}")
    
    # 检查ELF文件中的符号
    if firmware_file.endswith('.elf'):
        try:
            result = subprocess.run(["nm", firmware_file], 
                                  capture_output=True, text=True, check=True)
            
            symbols_to_check = [
                "_binary_src_voice_rom_bin_start",
                "_binary_src_voice_rom_bin_end"
            ]
            
            for symbol in symbols_to_check:
                if symbol in result.stdout:
                    print(f"✅ 找到符号: {symbol}")
                else:
                    print(f"❌ 缺失符号: {symbol}")
                    
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ 无法检查符号 (nm工具不可用)")
    
    return True

def main():
    print("=== 嵌入式ROM文件测试工具 ===")
    print("此工具验证ROM文件是否正确嵌入到固件中\n")
    
    success = True
    
    # 1. 检查ROM文件
    print("=== 检查ROM文件 ===")
    if not check_rom_file():
        print("需要先生成ROM文件...")
        try:
            subprocess.run(["python", "src/scripts/build_voice_rom.py", "--project-dir", "."], 
                         check=True)
            if not check_rom_file():
                success = False
        except:
            print("❌ 无法生成ROM文件")
            success = False
    
    # 2. 检查配置
    print("\n=== 检查PlatformIO配置 ===")
    if not check_platformio_config():
        success = False
    
    # 3. 构建和检查
    if success:
        if not build_and_check():
            success = False
        
        if not check_binary_symbols():
            success = False
    
    # 总结
    print("\n" + "="*50)
    if success:
        print("🎉 所有检查通过！")
        print("\n现在ROM文件应该已经嵌入到固件中。")
        print("上传固件后，ROM系统应该能够从嵌入数据正常初始化。")
        print("\n预期的日志输出:")
        print("  [VoiceROM] Loading ROM data from embedded binary...")
        print("  [VoiceROM] Embedded ROM header validated, writing to partition...")
        print("  [VoiceROM] Successfully loaded ROM data from embedded binary to partition")
    else:
        print("❌ 存在问题需要修复")
        print("\n请检查上述错误并修复后重试。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
